"use client";

import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { motion } from "framer-motion";
import { ArrowRight, Calendar, CheckCircle, Clock, Download, Mail, MapPin, Phone, Share2, Users } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { useSearchParams } from "next/navigation";
import { useEffect, useState } from "react";

interface BookingData {
	bookingNumber: string;
	customerName: string;
	email: string;
	phone: string;
	bookingDate: string;
	totalAmount: number;
	qrCode: string;
	items: {
		service: string;
		date: string;
		time: string;
		participants: number;
		price: number;
		image: string;
	}[];
}

export default function ConfirmationPage() {
	const [showConfetti, setShowConfetti] = useState(true);
	const [bookingData, setBookingData] = useState<BookingData | null>(null);
	const [loading, setLoading] = useState(true);
	const [error, setError] = useState<string | null>(null);

	const searchParams = useSearchParams();
	const bookingId = searchParams.get("booking");

	useEffect(() => {
		// Hide confetti after animation
		const timer = setTimeout(() => setShowConfetti(false), 3000);
		return () => clearTimeout(timer);
	}, []);

	useEffect(() => {
		const fetchBookingData = async () => {
			if (!bookingId) {
				setError("ID de réservation manquant");
				setLoading(false);
				return;
			}

			try {
				const response = await fetch(`/api/bookings/${bookingId}`);
				if (!response.ok) {
					throw new Error("Réservation non trouvée");
				}

				const data = await response.json();
				setBookingData(data.data);
			} catch (err) {
				console.error("Error fetching booking:", err);
				setError(err instanceof Error ? err.message : "Erreur lors du chargement");
			} finally {
				setLoading(false);
			}
		};

		fetchBookingData();
	}, [bookingId]);

	const formatDate = (dateString: string) => {
		const [year, month, day] = dateString.split("-").map(Number);
		const date = new Date(year, month - 1, day);
		return date.toLocaleDateString("fr-FR", {
			weekday: "long",
			year: "numeric",
			month: "long",
			day: "numeric",
		});
	};

	const handleDownloadPDF = async () => {
		if (!bookingData) return;

		try {
			// Import PDF generation functions
			const { generateBookingConfirmationPDF, downloadPDF } = await import("@/lib/pdf-generator");
			const { generateVerificationUrl } = await import("@/lib/qr-code");

			// Generate verification URL
			const verificationUrl = generateVerificationUrl(bookingData.bookingNumber, window.location.origin);

			// Generate PDF
			const pdfBlob = await generateBookingConfirmationPDF({
				reservationNumber: bookingData.bookingNumber,
				customerName: bookingData.customerName,
				email: bookingData.email,
				phone: bookingData.phone,
				serviceName: bookingData.items[0]?.service || "Service",
				date: bookingData.items[0]?.date || "",
				time: bookingData.items[0]?.time || "",
				participants: bookingData.items[0]?.participants || 1,
				totalAmount: bookingData.totalAmount,
				qrCodeData: {
					reservationId: bookingData.bookingNumber,
					reservationNumber: bookingData.bookingNumber,
					customerName: bookingData.customerName,
					serviceName: bookingData.items[0]?.service || "Service",
					date: bookingData.items[0]?.date || "",
					time: bookingData.items[0]?.time || "",
					participants: bookingData.items[0]?.participants || 1,
					totalAmount: bookingData.totalAmount,
					verificationUrl: verificationUrl,
				},
			});

			// Download the PDF
			downloadPDF(pdfBlob, `confirmation-${bookingData.bookingNumber}.pdf`);
		} catch (error) {
			console.error("Error generating PDF:", error);
			alert("Erreur lors de la génération du PDF. Veuillez réessayer.");
		}
	};

	const handleShare = () => {
		// Check if we're on the client side
		if (typeof window === "undefined" || !bookingData) return;

		// Simulate sharing
		if (navigator.share) {
			navigator.share({
				title: "Ma réservation Soleil & Découverte",
				text: `J'ai réservé mes excursions en Guadeloupe ! Numéro de réservation: ${bookingData.bookingNumber}`,
				url: window.location.href,
			});
		} else {
			// Fallback for browsers that don't support Web Share API
			navigator.clipboard.writeText(window.location.href);
			alert("Lien copié dans le presse-papiers !");
		}
	};

	if (loading) {
		return (
			<div className="min-h-screen bg-gradient-to-b from-sky-50 to-emerald-50 flex items-center justify-center">
				<div className="text-center">
					<div className="animate-spin rounded-full h-32 w-32 border-b-2 border-emerald-500 mx-auto"></div>
					<p className="mt-4 text-lg text-gray-600">Chargement de votre réservation...</p>
				</div>
			</div>
		);
	}

	if (error || !bookingData) {
		return (
			<div className="min-h-screen bg-gradient-to-b from-sky-50 to-emerald-50 flex items-center justify-center">
				<div className="text-center">
					<div className="text-red-500 text-6xl mb-4">⚠️</div>
					<h1 className="text-2xl font-bold text-gray-800 mb-2">Erreur</h1>
					<p className="text-gray-600 mb-4">{error || "Réservation non trouvée"}</p>
					<Link href="/reservation">
						<Button>Retour aux réservations</Button>
					</Link>
				</div>
			</div>
		);
	}

	return (
		<div className="min-h-screen bg-gradient-to-b from-sky-50 to-emerald-50 relative overflow-hidden">
			{/* Confetti Animation */}
			{showConfetti && typeof window !== "undefined" && (
				<div className="fixed inset-0 pointer-events-none z-10">
					{[...Array(50)].map((_, i) => (
						<motion.div
							key={i}
							className="absolute w-2 h-2 bg-emerald-500 rounded-full"
							initial={{
								x: Math.random() * (window?.innerWidth || 1200),
								y: -10,
								rotate: 0,
							}}
							animate={{
								y: (window?.innerHeight || 800) + 10,
								rotate: 360,
							}}
							transition={{
								duration: Math.random() * 3 + 2,
								ease: "linear",
								delay: Math.random() * 2,
							}}
						/>
					))}
				</div>
			)}

			{/* Header */}
			<header className="bg-white/90 backdrop-blur-md shadow-sm">
				<div className="container mx-auto px-4 py-4">
					<div className="flex items-center justify-between">
						<Link href="/" className="flex items-center space-x-3">
							<Image
								src="/images/logo-hd.png"
								alt="Soleil & Découverte"
								width={60}
								height={60}
								className="object-contain"
							/>
							<div>
								<h1 className="text-2xl font-bold bg-gradient-to-r from-orange-500 to-yellow-500 bg-clip-text text-transparent">
									Soleil & Découverte
								</h1>
								<p className="text-sm text-emerald-600">Confirmation de réservation</p>
							</div>
						</Link>
					</div>
				</div>
			</header>

			<div className="container mx-auto px-4 py-8">
				<div className="max-w-4xl mx-auto">
					{/* Success Message */}
					<motion.div
						initial={{ opacity: 0, y: 30 }}
						animate={{ opacity: 1, y: 0 }}
						transition={{ duration: 0.6 }}
						className="text-center mb-8"
					>
						<motion.div
							initial={{ scale: 0 }}
							animate={{ scale: 1 }}
							transition={{ duration: 0.5, delay: 0.2 }}
							className="inline-flex items-center justify-center w-20 h-20 bg-emerald-500 text-white rounded-full mb-6"
						>
							<CheckCircle className="w-10 h-10" />
						</motion.div>

						<h1 className="text-4xl font-bold text-gray-900 mb-4">Réservation confirmée !</h1>
						<p className="text-xl text-gray-600 mb-2">
							Merci {bookingData.customerName}, votre aventure guadeloupéenne vous attend !
						</p>
						<p className="text-lg text-emerald-600 font-semibold">
							Numéro de réservation : {bookingData.bookingNumber}
						</p>
					</motion.div>

					{/* Quick Actions */}
					<motion.div
						initial={{ opacity: 0, y: 20 }}
						animate={{ opacity: 1, y: 0 }}
						transition={{ duration: 0.6, delay: 0.3 }}
						className="flex flex-wrap justify-center gap-4 mb-8"
					>
						<Button onClick={handleDownloadPDF} className="flex items-center gap-2">
							<Download className="w-4 h-4" />
							Télécharger PDF
						</Button>
						<Button variant="outline" onClick={handleShare} className="flex items-center gap-2">
							<Share2 className="w-4 h-4" />
							Partager
						</Button>
					</motion.div>

					<div className="grid lg:grid-cols-3 gap-8">
						{/* Booking Details */}
						<div className="lg:col-span-2 space-y-6">
							{/* Booking Summary */}
							<motion.div
								initial={{ opacity: 0, x: -20 }}
								animate={{ opacity: 1, x: 0 }}
								transition={{ duration: 0.6, delay: 0.4 }}
							>
								<Card>
									<CardHeader>
										<CardTitle className="flex items-center gap-2">
											<Calendar className="w-5 h-5" />
											Détails de votre réservation
										</CardTitle>
									</CardHeader>
									<CardContent>
										<div className="space-y-6">
											{bookingData.items.map((item, index) => (
												<div key={index} className="flex gap-4 p-4 bg-gray-50 rounded-lg">
													<Image
														src={item.image || "/placeholder.svg"}
														alt={item.service}
														width={80}
														height={80}
														className="rounded-lg object-cover"
													/>
													<div className="flex-1">
														<h3 className="font-semibold text-lg">{item.service}</h3>
														<div className="flex items-center gap-4 text-sm text-gray-600 mt-2">
															<div className="flex items-center gap-1">
																<Calendar className="w-4 h-4" />
																{formatDate(item.date)}
															</div>
															<div className="flex items-center gap-1">
																<Clock className="w-4 h-4" />
																{item.time}
															</div>
															<div className="flex items-center gap-1">
																<Users className="w-4 h-4" />
																{item.participants} pers.
															</div>
														</div>
													</div>
													<div className="text-right">
														<div className="text-lg font-bold text-emerald-600">
															{item.price}€
														</div>
													</div>
												</div>
											))}
										</div>
									</CardContent>
								</Card>
							</motion.div>

							{/* Important Information */}
							<motion.div
								initial={{ opacity: 0, x: -20 }}
								animate={{ opacity: 1, x: 0 }}
								transition={{ duration: 0.6, delay: 0.5 }}
							>
								<Card className="bg-blue-50 border-blue-200">
									<CardHeader>
										<CardTitle className="text-blue-800">Informations importantes</CardTitle>
									</CardHeader>
									<CardContent className="text-blue-700">
										<ul className="space-y-2 text-sm">
											<li>
												• Rendez-vous 15 minutes avant le départ au port de pêche de Petit-Canal
											</li>
											<li>• Apportez : crème solaire, chapeau, maillot de bain et serviette</li>
											<li>• Annulation gratuite jusqu'à 24h avant l'excursion</li>
											<li>• En cas de mauvais temps, nous vous contacterons pour reporter</li>
											<li>• Collations et équipements fournis selon l'excursion</li>
										</ul>
									</CardContent>
								</Card>
							</motion.div>

							{/* Contact Information */}
							<motion.div
								initial={{ opacity: 0, x: -20 }}
								animate={{ opacity: 1, x: 0 }}
								transition={{ duration: 0.6, delay: 0.6 }}
							>
								<Card>
									<CardHeader>
										<CardTitle className="flex items-center gap-2">
											<MapPin className="w-5 h-5" />
											Lieu de rendez-vous
										</CardTitle>
									</CardHeader>
									<CardContent>
										<div className="space-y-4">
											<div>
												<h4 className="font-semibold">Port de pêche de Petit-Canal</h4>
												<p className="text-gray-600">
													Rue de la Darse, 97131 Petit-Canal, Guadeloupe
												</p>
											</div>

											<div className="grid md:grid-cols-2 gap-4">
												<div className="flex items-center gap-2">
													<Phone className="w-4 h-4 text-emerald-600" />
													<span>+33 6 40 24 44 25</span>
												</div>
												<div className="flex items-center gap-2">
													<Mail className="w-4 h-4 text-emerald-600" />
													<span><EMAIL></span>
												</div>
											</div>

											<Button variant="outline" className="w-full">
												<MapPin className="w-4 h-4 mr-2" />
												Voir sur Google Maps
											</Button>
										</div>
									</CardContent>
								</Card>
							</motion.div>
						</div>

						{/* Sidebar */}
						<div className="space-y-6">
							{/* Payment Summary */}
							<motion.div
								initial={{ opacity: 0, x: 20 }}
								animate={{ opacity: 1, x: 0 }}
								transition={{ duration: 0.6, delay: 0.4 }}
							>
								<Card>
									<CardHeader>
										<CardTitle>Récapitulatif de commande</CardTitle>
									</CardHeader>
									<CardContent>
										<div className="space-y-4">
											{bookingData.items.map((item, index) => (
												<div key={index} className="space-y-2">
													<h3 className="font-semibold text-gray-900">{item.service}</h3>
													<div className="text-sm text-gray-600">
														{formatDate(item.date)} à {item.time}
													</div>
													<div className="flex justify-between">
														<span>
															{item.participants} participant
															{item.participants > 1 ? "s" : ""}
														</span>
														<span className="font-semibold text-lg">{item.price}€</span>
													</div>
												</div>
											))}

											<Separator className="my-4" />

											<div className="flex justify-between">
												<span className="text-lg font-semibold">Total</span>
												<span className="text-xl font-bold text-emerald-600">
													{bookingData.totalAmount}€
												</span>
											</div>
										</div>
									</CardContent>
								</Card>
							</motion.div>

							{/* Customer Info */}
							<motion.div
								initial={{ opacity: 0, x: 20 }}
								animate={{ opacity: 1, x: 0 }}
								transition={{ duration: 0.6, delay: 0.5 }}
							>
								<Card>
									<CardHeader>
										<CardTitle>Vos informations</CardTitle>
									</CardHeader>
									<CardContent>
										<div className="space-y-3 text-sm">
											<div>
												<span className="font-medium">Nom :</span>
												<span className="ml-2">{bookingData.customerName}</span>
											</div>
											<div>
												<span className="font-medium">Email :</span>
												<span className="ml-2">{bookingData.email}</span>
											</div>
											<div>
												<span className="font-medium">Téléphone :</span>
												<span className="ml-2">{bookingData.phone}</span>
											</div>
										</div>
									</CardContent>
								</Card>
							</motion.div>

							{/* QR Code */}
							<motion.div
								initial={{ opacity: 0, x: 20 }}
								animate={{ opacity: 1, x: 0 }}
								transition={{ duration: 0.6, delay: 0.6 }}
							>
								<Card>
									<CardHeader>
										<CardTitle>Code QR de Vérification</CardTitle>
									</CardHeader>
									<CardContent className="text-center">
										<div className="flex justify-center mb-4">
											{bookingData.qrCode ? (
												<img
													src={bookingData.qrCode}
													alt="QR Code de réservation"
													className="w-32 h-32 border rounded-lg"
												/>
											) : (
												<div className="w-32 h-32 bg-gray-200 border rounded-lg flex items-center justify-center">
													<span className="text-gray-500 text-sm">QR Code</span>
												</div>
											)}
										</div>
										<p className="text-sm text-gray-600">
											Présentez ce code QR lors de votre excursion pour une vérification rapide
										</p>
									</CardContent>
								</Card>
							</motion.div>
						</div>
					</div>

					{/* Call to Action */}
					<motion.div
						initial={{ opacity: 0, y: 20 }}
						animate={{ opacity: 1, y: 0 }}
						transition={{ duration: 0.6, delay: 0.8 }}
						className="text-center mt-12"
					>
						<Card className="bg-gradient-to-r from-emerald-500 to-teal-600 text-white">
							<CardContent className="p-8">
								<h2 className="text-2xl font-bold mb-4">Découvrez d'autres aventures</h2>
								<p className="mb-6 opacity-90">
									Explorez nos autres excursions pour vivre une expérience complète de la Guadeloupe
								</p>
								<div className="flex flex-col sm:flex-row gap-4 justify-center">
									<Link href="/services">
										<Button variant="secondary" className="flex items-center gap-2">
											Voir toutes les excursions
											<ArrowRight className="w-4 h-4" />
										</Button>
									</Link>
									<Link href="/">
										<Button variant="secondary" className="flex items-center gap-2">
											Retour à l'accueil
										</Button>
									</Link>
								</div>
							</CardContent>
						</Card>
					</motion.div>
				</div>
			</div>
		</div>
	);
}
