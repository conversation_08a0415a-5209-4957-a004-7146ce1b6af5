"use client";

import type React from "react";

import { Accordion, AccordionContent, AccordionI<PERSON>, AccordionTrigger } from "@/components/ui/accordion";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { motion } from "framer-motion";
import { AlertCircle, Anchor, CheckCircle, Clock, Mail, MapPin, MessageSquare, Phone, Send } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { useState } from "react";

const faqData = [
	{
		question: "Comment réserver une excursion ?",
		answer: "Vous pouvez réserver directement par téléphone au +33 6 52 73 34 91, ou par email à <EMAIL>. Nous vous recommandons de réserver à l'avance pour garantir votre place.",
	},
	{
		question: "Quels sont les tarifs pour les enfants ?",
		answer: "Nos excursions sont adaptées aux familles : gratuit pour les moins de 6 ans pour la rencontre avec les pélicans, gratuit pour les moins de 12 ans pour les visites culturelles, et à partir de 12 ans pour les WaterBikes.",
	},
	{
		question: "Que se passe-t-il en cas de mauvais temps ?",
		answer: "La sécurité de nos clients est notre priorité. En cas de conditions météorologiques défavorables, nous reportons ou annulons l'excursion. Vous serez remboursé intégralement ou pourrez reporter votre réservation sans frais.",
	},
	{
		question: "Que dois-je apporter pour l'excursion ?",
		answer: "Nous recommandons : crème solaire, chapeau, lunettes de soleil, maillot de bain, serviette, et une bouteille d'eau. Nous fournissons les collations, sacs étanches et jumelles d'observation selon l'excursion.",
	},
	{
		question: "Vos excursions sont-elles éco-responsables ?",
		answer: "Absolument ! Toutes nos activités sont pensées pour minimiser l'impact sur l'environnement, respecter la biodiversité et sensibiliser à la préservation de notre patrimoine naturel guadeloupéen.",
	},
	{
		question: "Proposez-vous des dégustations de produits locaux ?",
		answer: "Oui ! Nous proposons des expériences gustatives avec des produits locaux authentiques. Chaque bouchée est une invitation au voyage avec des saveurs que vous ne trouverez nulle part ailleurs.",
	},
	{
		question: "Où se déroulent vos excursions ?",
		answer: "Nos excursions partent du port de pêche de Petit-Canal et nous emmènent découvrir les îlets secrets, la mangrove, les sites historiques et les plus beaux spots de la Guadeloupe.",
	},
	{
		question: "Quels équipements fournissez-vous ?",
		answer: "Selon l'excursion : WaterBikes, sacs étanches, jumelles d'observation, collations, et tout l'équipement de sécurité nécessaire. Nos guides expérimentés vous accompagnent pour une expérience en toute sérénité.",
	},
];

export default function ContactPage() {
	const [formData, setFormData] = useState({
		name: "",
		email: "",
		phone: "",
		subject: "",
		message: "",
		excursion: "",
		participants: "",
	});
	const [isSubmitting, setIsSubmitting] = useState(false);
	const [isSubmitted, setIsSubmitted] = useState(false);
	const [errors, setErrors] = useState<Record<string, string>>({});
	const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

	const validateForm = () => {
		const newErrors: Record<string, string> = {};

		if (!formData.name.trim()) {
			newErrors.name = "Le nom est requis";
		}

		if (!formData.email.trim()) {
			newErrors.email = "L'email est requis";
		} else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
			newErrors.email = "Format d'email invalide";
		}

		if (!formData.subject.trim()) {
			newErrors.subject = "Le sujet est requis";
		}

		if (!formData.message.trim()) {
			newErrors.message = "Le message est requis";
		} else if (formData.message.length < 10) {
			newErrors.message = "Le message doit contenir au moins 10 caractères";
		}

		setErrors(newErrors);
		return Object.keys(newErrors).length === 0;
	};

	const handleSubmit = async (e: React.FormEvent) => {
		e.preventDefault();

		if (!validateForm()) {
			return;
		}

		setIsSubmitting(true);

		// Simulate form submission
		await new Promise((resolve) => setTimeout(resolve, 2000));

		console.log("Formulaire soumis:", formData);
		setIsSubmitted(true);
		setIsSubmitting(false);

		// Reset form after 3 seconds
		setTimeout(() => {
			setIsSubmitted(false);
			setFormData({
				name: "",
				email: "",
				phone: "",
				subject: "",
				message: "",
				excursion: "",
				participants: "",
			});
		}, 3000);
	};

	const handleInputChange = (field: string, value: string) => {
		setFormData((prev) => ({ ...prev, [field]: value }));
		if (errors[field]) {
			setErrors((prev) => ({ ...prev, [field]: "" }));
		}
	};

	return (
		<div className="min-h-screen bg-gradient-to-b from-sky-50 to-emerald-50">
			{/* Header */}
			<header className="bg-white/90 backdrop-blur-md shadow-sm sticky top-0 z-50">
				<div className="container mx-auto px-4 py-4">
					<div className="flex items-center justify-between">
						<Link href="/" className="flex items-center space-x-3">
							<Image
								src="/images/logo-hd.png"
								alt="Soleil & Découverte"
								width={60}
								height={60}
								className="object-contain"
							/>
							<div>
								<h1 className="text-2xl font-bold bg-gradient-to-r from-orange-500 to-yellow-500 bg-clip-text text-transparent">
									Soleil & Découverte
								</h1>
								<p className="text-sm text-emerald-600">L'aventure au cœur de la Guadeloupe</p>
							</div>
						</Link>

						<nav className="hidden md:flex items-center space-x-8">
							<Link
								href="/"
								className="text-gray-700 hover:text-emerald-600 transition-colors font-medium"
							>
								Accueil
							</Link>
							<Link
								href="/services"
								className="text-gray-700 hover:text-emerald-600 transition-colors font-medium"
							>
								Services
							</Link>
							<Link href="/contact" className="text-emerald-600 font-semibold">
								Contact
							</Link>
							<Link href="/reservation">
								<Button className="bg-gradient-to-r from-emerald-500 to-teal-600 hover:from-emerald-600 hover:to-teal-700 text-white px-6 py-2 rounded-full shadow-lg hover:shadow-xl transition-all duration-300">
									Réserver
								</Button>
							</Link>
						</nav>

						{/* Mobile menu button */}
						<Button
							variant="ghost"
							className="md:hidden"
							onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
						>
							<svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path
									strokeLinecap="round"
									strokeLinejoin="round"
									strokeWidth={2}
									d="M4 6h16M4 12h16M4 18h16"
								/>
							</svg>
						</Button>
					</div>
				</div>
			</header>

			{/* Mobile Menu */}
			{isMobileMenuOpen && (
				<div className="md:hidden bg-white border-b border-gray-200 shadow-lg">
					<div className="container mx-auto px-4 py-4">
						<nav className="flex flex-col space-y-4">
							<Link
								href="/"
								className="text-gray-700 hover:text-emerald-600 transition-colors font-medium py-2"
								onClick={() => setIsMobileMenuOpen(false)}
							>
								Accueil
							</Link>
							<Link
								href="/services"
								className="text-gray-700 hover:text-emerald-600 transition-colors font-medium py-2"
								onClick={() => setIsMobileMenuOpen(false)}
							>
								Services
							</Link>
							<Link
								href="/contact"
								className="text-emerald-600 font-semibold py-2"
								onClick={() => setIsMobileMenuOpen(false)}
							>
								Contact
							</Link>
							<Link href="/reservation" onClick={() => setIsMobileMenuOpen(false)}>
								<Button className="bg-gradient-to-r from-emerald-500 to-teal-600 hover:from-emerald-600 hover:to-teal-700 text-white px-6 py-2 rounded-full shadow-lg hover:shadow-xl transition-all duration-300 w-full">
									Réserver
								</Button>
							</Link>
						</nav>
					</div>
				</div>
			)}

			{/* Hero Section */}
			<section className="relative py-20 overflow-hidden">
				<Image
					src="/images/contact_hero.png"
					alt="Contact Hero"
					fill
					className="object-cover"
					style={{ zIndex: 1 }}
				/>
				<div className="absolute inset-0 bg-black/20" style={{ zIndex: 2 }}></div>

				<div className="container mx-auto px-4 relative z-10">
					<motion.div
						initial={{ opacity: 0, y: 30 }}
						animate={{ opacity: 1, y: 0 }}
						transition={{ duration: 0.8 }}
						className="text-center max-w-4xl mx-auto"
					>
						<Badge className="mb-6 bg-white/20 text-white border-white/30 backdrop-blur-sm px-4 py-2 text-lg">
							📞 Contact & Infos
						</Badge>
						<h1 className="text-4xl md:text-6xl font-bold text-white mb-6">
							Parlons de Votre
							<span className="block bg-gradient-to-r from-yellow-400 to-orange-500 bg-clip-text text-transparent">
								Prochaine Aventure
							</span>
						</h1>
						<p className="text-xl text-white/90 max-w-3xl mx-auto">
							Notre équipe est là pour répondre à toutes vos questions et vous aider à planifier
							l'excursion parfaite en Guadeloupe.
						</p>
					</motion.div>
				</div>
			</section>

			{/* Contact Info - Condensed */}
			<section className="py-8 -mt-12 relative z-10">
				<div className="container mx-auto px-4">
					<div className="max-w-4xl mx-auto">
						<Card className="shadow-2xl border-0 bg-white/95 backdrop-blur-sm">
							<CardContent className="p-8">
								<div className="grid md:grid-cols-4 gap-6 text-center">
									<div>
										<Phone className="w-6 h-6 text-emerald-600 mx-auto mb-2" />
										<p className="font-semibold text-gray-900">+33 6 52 73 34 91</p>
										<p className="text-sm text-gray-500">Tous les jours</p>
									</div>
									<div className="flex flex-col items-center">
										<Mail className="w-6 h-6 text-blue-600 mb-2" />
										<p className="font-semibold text-gray-900 text-sm text-center">
											<EMAIL>
										</p>
										<p className="text-sm text-gray-500 text-center">Réponse rapide</p>
									</div>
									<div>
										<MapPin className="w-6 h-6 text-orange-600 mx-auto mb-2" />
										<p className="font-semibold text-gray-900">Port de pêche</p>
										<p className="text-sm text-gray-500">Petit-Canal, 97131</p>
									</div>
									<div>
										<Clock className="w-6 h-6 text-purple-600 mx-auto mb-2" />
										<p className="font-semibold text-gray-900">Flexible</p>
										<p className="text-sm text-gray-500">Selon marées</p>
									</div>
								</div>
							</CardContent>
						</Card>
					</div>
				</div>
			</section>

			{/* Main Content - Condensed */}
			<section className="py-12">
				<div className="container mx-auto px-4">
					<div className="max-w-6xl mx-auto grid lg:grid-cols-3 gap-8">
						{/* Contact Form - Takes 2 columns */}
						<div className="lg:col-span-2">
							<Card className="shadow-2xl border-0 bg-white/95 backdrop-blur-sm">
								<CardHeader>
									<CardTitle className="text-2xl font-bold text-gray-900 flex items-center gap-2">
										<MessageSquare className="w-6 h-6 text-emerald-600" />
										Contactez-nous
									</CardTitle>
									<p className="text-gray-600">
										Remplissez ce formulaire et nous vous répondrons dans les plus brefs délais.
									</p>
								</CardHeader>
								<CardContent>
									{isSubmitted ? (
										<motion.div
											initial={{ opacity: 0, scale: 0.9 }}
											animate={{ opacity: 1, scale: 1 }}
											className="text-center py-8"
										>
											<CheckCircle className="w-16 h-16 text-green-500 mx-auto mb-4" />
											<h3 className="text-xl font-semibold text-gray-900 mb-2">
												Message envoyé !
											</h3>
											<p className="text-gray-600">
												Merci pour votre message. Nous vous répondrons rapidement.
											</p>
										</motion.div>
									) : (
										<form onSubmit={handleSubmit} className="space-y-4">
											<div className="grid md:grid-cols-2 gap-4">
												<div>
													<label
														htmlFor="name"
														className="block text-sm font-medium text-gray-700 mb-1"
													>
														Nom complet *
													</label>
													<Input
														id="name"
														value={formData.name}
														onChange={(e) => handleInputChange("name", e.target.value)}
														className={`${errors.name ? "border-red-500" : ""}`}
														placeholder="Votre nom"
													/>
													{errors.name && (
														<p className="text-red-500 text-sm mt-1 flex items-center gap-1">
															<AlertCircle className="w-4 h-4" />
															{errors.name}
														</p>
													)}
												</div>

												<div>
													<label
														htmlFor="email"
														className="block text-sm font-medium text-gray-700 mb-1"
													>
														Email *
													</label>
													<Input
														id="email"
														type="email"
														value={formData.email}
														onChange={(e) => handleInputChange("email", e.target.value)}
														className={`${errors.email ? "border-red-500" : ""}`}
														placeholder="<EMAIL>"
													/>
													{errors.email && (
														<p className="text-red-500 text-sm mt-1 flex items-center gap-1">
															<AlertCircle className="w-4 h-4" />
															{errors.email}
														</p>
													)}
												</div>
											</div>

											<div className="grid md:grid-cols-2 gap-4">
												<div>
													<label
														htmlFor="phone"
														className="block text-sm font-medium text-gray-700 mb-1"
													>
														Téléphone
													</label>
													<Input
														id="phone"
														value={formData.phone}
														onChange={(e) => handleInputChange("phone", e.target.value)}
														placeholder="+33 6 XX XX XX XX"
													/>
												</div>

												<div>
													<label
														htmlFor="participants"
														className="block text-sm font-medium text-gray-700 mb-1"
													>
														Nombre de participants
													</label>
													<Input
														id="participants"
														type="number"
														min="1"
														value={formData.participants}
														onChange={(e) =>
															handleInputChange("participants", e.target.value)
														}
														placeholder="2"
													/>
												</div>
											</div>

											<div className="grid md:grid-cols-2 gap-4">
												<div>
													<label
														htmlFor="subject"
														className="block text-sm font-medium text-gray-700 mb-1"
													>
														Sujet *
													</label>
													<Input
														id="subject"
														value={formData.subject}
														onChange={(e) => handleInputChange("subject", e.target.value)}
														className={`${errors.subject ? "border-red-500" : ""}`}
														placeholder="Réservation, question..."
													/>
													{errors.subject && (
														<p className="text-red-500 text-sm mt-1 flex items-center gap-1">
															<AlertCircle className="w-4 h-4" />
															{errors.subject}
														</p>
													)}
												</div>

												<div>
													<label
														htmlFor="excursion"
														className="block text-sm font-medium text-gray-700 mb-1"
													>
														Excursion souhaitée
													</label>
													<Input
														id="excursion"
														value={formData.excursion}
														onChange={(e) => handleInputChange("excursion", e.target.value)}
														placeholder="WaterBike, Pélicans, Visite culturelle..."
													/>
												</div>
											</div>

											<div>
												<label
													htmlFor="message"
													className="block text-sm font-medium text-gray-700 mb-1"
												>
													Message *
												</label>
												<Textarea
													id="message"
													rows={4}
													value={formData.message}
													onChange={(e) => handleInputChange("message", e.target.value)}
													className={`${errors.message ? "border-red-500" : ""}`}
													placeholder="Décrivez votre demande..."
												/>
												{errors.message && (
													<p className="text-red-500 text-sm mt-1 flex items-center gap-1">
														<AlertCircle className="w-4 h-4" />
														{errors.message}
													</p>
												)}
											</div>

											<Button
												type="submit"
												disabled={isSubmitting}
												className="w-full bg-gradient-to-r from-emerald-500 to-teal-600 hover:from-emerald-600 hover:to-teal-700 text-white py-3 rounded-full shadow-lg hover:shadow-xl transition-all duration-300"
											>
												{isSubmitting ? (
													<div className="flex items-center gap-2">
														<div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
														Envoi en cours...
													</div>
												) : (
													<div className="flex items-center gap-2">
														<Send className="w-4 h-4" />
														Envoyer le message
													</div>
												)}
											</Button>
										</form>
									)}
								</CardContent>
							</Card>
						</div>

						{/* Right Sidebar - Takes 1 column */}
						<div className="space-y-6">
							{/* Embedded Map */}
							<Card className="shadow-xl border-0 bg-white/95 backdrop-blur-sm overflow-hidden">
								<div className="relative h-64">
									<iframe
										src="https://maps.google.com/maps?width=100%25&amp;height=600&amp;hl=en&amp;q=Port%20de%20p%C3%AAche,%20Esc.%20aux%20Esclaves,%20Petit-Canal%2097131,%20Guadeloupe+(My%20Business%20Name)&amp;t=&amp;z=16&amp;ie=UTF8&amp;iwloc=B&amp;output=embed"
										width="100%"
										height="100%"
										style={{ border: 0 }}
										allowFullScreen
										loading="lazy"
										referrerPolicy="no-referrer-when-downgrade"
										className="rounded-t-xl"
									/>
								</div>
								<div className="p-4">
									<h3 className="font-semibold text-gray-900 mb-1">Port de pêche de Petit-Canal</h3>
									<p className="text-sm text-gray-600">Rue de la Darse, 97131 Guadeloupe</p>
								</div>
							</Card>

							{/* Infos Pratiques - Condensed */}
							<Card className="shadow-xl border-0 bg-white/95 backdrop-blur-sm">
								<CardContent className="p-6">
									<h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
										<Anchor className="w-5 h-5 text-emerald-600" />
										Infos Pratiques
									</h3>
									<div className="space-y-3 text-sm">
										<div className="flex items-center justify-between">
											<span className="text-gray-600">Réservation</span>
											<span className="font-medium text-emerald-600">Recommandée</span>
										</div>
										<div className="flex items-center justify-between">
											<span className="text-gray-600">Enfants</span>
											<span className="font-medium text-emerald-600">Tarifs préférentiels</span>
										</div>
										<div className="flex items-center justify-between">
											<span className="text-gray-600">Équipement</span>
											<span className="font-medium text-emerald-600">Fourni</span>
										</div>
										<div className="flex items-center justify-between">
											<span className="text-gray-600">Éco-responsable</span>
											<span className="font-medium text-emerald-600">100%</span>
										</div>
									</div>
								</CardContent>
							</Card>
						</div>
					</div>
				</div>
			</section>

			{/* FAQ Section */}
			<section className="py-20 bg-white">
				<div className="container mx-auto px-4">
					<motion.div
						initial={{ opacity: 0, y: 30 }}
						whileInView={{ opacity: 1, y: 0 }}
						transition={{ duration: 0.8 }}
						className="text-center mb-16"
					>
						<Badge className="mb-4 bg-gradient-to-r from-blue-100 to-indigo-100 text-blue-700 border-blue-200 px-4 py-2">
							❓ Questions Fréquentes
						</Badge>
						<h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
							Tout Ce Que Vous
							<span className="block text-emerald-600">Devez Savoir</span>
						</h2>
						<p className="text-xl text-gray-600 max-w-3xl mx-auto">
							Retrouvez les réponses aux questions les plus fréquemment posées par nos clients.
						</p>
					</motion.div>

					<motion.div
						initial={{ opacity: 0, y: 50 }}
						whileInView={{ opacity: 1, y: 0 }}
						transition={{ duration: 0.8 }}
						className="max-w-4xl mx-auto"
					>
						<Accordion type="single" collapsible className="space-y-4">
							{faqData.map((faq, index) => (
								<AccordionItem
									key={index}
									value={`item-${index}`}
									className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg border-0 px-6"
								>
									<AccordionTrigger className="text-left font-semibold text-gray-900 hover:text-emerald-600 transition-colors py-6">
										{faq.question}
									</AccordionTrigger>
									<AccordionContent className="text-gray-600 leading-relaxed pb-6">
										{faq.answer}
									</AccordionContent>
								</AccordionItem>
							))}
						</Accordion>
					</motion.div>
				</div>
			</section>

			{/* CTA Section */}
			<section className="py-20 bg-gradient-to-br from-emerald-600 to-teal-700 relative overflow-hidden">
				<div className="absolute inset-0 bg-black/20"></div>
				<Image
					src="/images/contact_cta.png"
					alt="Contact Us"
					fill
					className="object-cover"
					style={{ zIndex: 1 }}
				/>

				<div className="container mx-auto px-4 relative z-10">
					<motion.div
						initial={{ opacity: 0, y: 30 }}
						whileInView={{ opacity: 1, y: 0 }}
						transition={{ duration: 0.8 }}
						className="text-center max-w-4xl mx-auto"
					>
						<h2 className="text-4xl md:text-5xl font-bold text-white mb-6">Prêt à Partir à l'Aventure ?</h2>
						<p className="text-xl text-white/90 mb-8">
							N'attendez plus ! Contactez-nous dès maintenant pour réserver votre excursion de rêve en
							Guadeloupe.
						</p>
						<div className="flex flex-col sm:flex-row gap-4 justify-center">
							<Link href="/reservation">
								<Button
									size="lg"
									className="bg-white text-emerald-600 hover:bg-gray-100 px-8 py-4 rounded-full shadow-2xl hover:shadow-3xl transition-all duration-300 text-lg font-semibold"
								>
									Nous Contacter
								</Button>
							</Link>
							<Link href="/services">
								<Button
									size="lg"
									variant="outline"
									className="border-2 border-white text-white hover:bg-white hover:text-emerald-600 px-8 py-4 rounded-full backdrop-blur-sm bg-white/10 transition-all duration-300 text-lg font-semibold"
								>
									Voir Nos Excursions
								</Button>
							</Link>
						</div>
					</motion.div>
				</div>
			</section>

			{/* Footer */}
			<footer className="bg-gray-900 text-white py-16">
				<div className="container mx-auto px-4">
					<div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
						<div>
							<div className="flex items-center space-x-3 mb-6">
								<Image
									src="/images/logo-hd.png"
									alt="Soleil & Découverte"
									width={50}
									height={50}
									className="object-contain"
								/>
								<div>
									<h3 className="text-xl font-bold bg-gradient-to-r from-orange-400 to-yellow-400 bg-clip-text text-transparent">
										Soleil & Découverte
									</h3>
									<p className="text-sm text-emerald-400">L'aventure au cœur de la Guadeloupe</p>
								</div>
							</div>
							<p className="text-gray-400 leading-relaxed">
								Découvrez la Guadeloupe authentique avec nos excursions éco-responsables.
							</p>
						</div>

						<div>
							<h4 className="text-lg font-semibold mb-4 text-emerald-400">Navigation</h4>
							<ul className="space-y-2">
								<li>
									<Link href="/" className="text-gray-400 hover:text-white transition-colors">
										Accueil
									</Link>
								</li>
								<li>
									<Link href="/services" className="text-gray-400 hover:text-white transition-colors">
										Services
									</Link>
								</li>
								<li>
									<Link href="/contact" className="text-gray-400 hover:text-white transition-colors">
										Contact
									</Link>
								</li>
							</ul>
						</div>

						<div>
							<h4 className="text-lg font-semibold mb-4 text-emerald-400">Nos Activités</h4>
							<ul className="space-y-2">
								<li>
									<span className="text-gray-400">Excursions WaterBikes</span>
								</li>
								<li>
									<span className="text-gray-400">Visites Culturelles</span>
								</li>
								<li>
									<span className="text-gray-400">Rencontre Pélicans</span>
								</li>
								<li>
									<span className="text-gray-400">Dégustations Locales</span>
								</li>
							</ul>
						</div>

						<div>
							<h4 className="text-lg font-semibold mb-4 text-emerald-400">Contact</h4>
							<div className="space-y-3 text-gray-400">
								<div className="flex items-center">
									<MapPin className="w-4 h-4 mr-2 text-emerald-400" />
									<span className="text-sm">Petit-Canal, Guadeloupe</span>
								</div>
								<div className="flex items-center">
									<svg
										className="w-4 h-4 mr-2 text-emerald-400"
										fill="currentColor"
										viewBox="0 0 20 20"
									>
										<path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z" />
									</svg>
									<span className="text-sm">+33 6 52 73 34 91</span>
								</div>
								<div className="flex items-center">
									<svg
										className="w-4 h-4 mr-2 text-emerald-400"
										fill="currentColor"
										viewBox="0 0 20 20"
									>
										<path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z" />
										<path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z" />
									</svg>
									<span className="text-sm"><EMAIL></span>
								</div>
							</div>
						</div>
					</div>

					<div className="border-t border-gray-800 mt-12 pt-8 text-center">
						<p className="text-gray-400">© 2024 Soleil & Découverte. Tous droits réservés.</p>
					</div>
				</div>
			</footer>
		</div>
	);
}
