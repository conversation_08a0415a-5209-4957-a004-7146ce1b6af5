import jsPDF from 'jspdf';

export interface InvoiceData {
  invoiceNumber: string;
  invoiceDate: string;
  dueDate?: string;
  customerName: string;
  customerEmail: string;
  customerAddress?: string;
  reservationNumber: string;
  serviceName: string;
  serviceDate: string;
  serviceTime: string;
  participants: number;
  unitPrice: number;
  totalAmount: number;
  currency: string;
  paymentStatus: 'paid' | 'partial' | 'pending';
  paidAmount?: number;
  remainingAmount?: number;
  paymentDate?: string;
  paymentMethod?: string;
  notes?: string;
}

/**
 * Generate PDF invoice for a reservation
 */
export async function generateInvoicePDF(data: InvoiceData): Promise<Blob> {
  try {
    // Create new PDF document
    const pdf = new jsPDF({
      orientation: 'portrait',
      unit: 'mm',
      format: 'a4',
    });

    // Company header
    pdf.setFontSize(24);
    pdf.setTextColor(16, 185, 129);
    pdf.text('Soleil & Découverte', 20, 30);
    
    pdf.setFontSize(12);
    pdf.setTextColor(100, 100, 100);
    pdf.text('Excursions éco-responsables en Guadeloupe', 20, 38);
    pdf.text('Email: <EMAIL>', 20, 45);
    pdf.text('Téléphone: +590 XXX XXX XXX', 20, 52);

    // Invoice title and number
    pdf.setFontSize(20);
    pdf.setTextColor(0, 0, 0);
    pdf.text('FACTURE', 150, 30);
    
    pdf.setFontSize(12);
    pdf.text(`N° ${data.invoiceNumber}`, 150, 38);
    pdf.text(`Date: ${data.invoiceDate}`, 150, 45);
    if (data.dueDate) {
      pdf.text(`Échéance: ${data.dueDate}`, 150, 52);
    }

    // Customer information
    pdf.setFontSize(14);
    pdf.setTextColor(16, 185, 129);
    pdf.text('Facturé à:', 20, 70);
    
    pdf.setFontSize(12);
    pdf.setTextColor(0, 0, 0);
    pdf.text(data.customerName, 20, 78);
    pdf.text(data.customerEmail, 20, 85);
    if (data.customerAddress) {
      const addressLines = data.customerAddress.split('\n');
      let yPos = 92;
      addressLines.forEach(line => {
        pdf.text(line, 20, yPos);
        yPos += 7;
      });
    }

    // Service details
    pdf.setFontSize(14);
    pdf.setTextColor(16, 185, 129);
    pdf.text('Détails du service:', 20, 120);
    
    pdf.setFontSize(12);
    pdf.setTextColor(0, 0, 0);
    pdf.text(`Réservation: ${data.reservationNumber}`, 20, 128);
    pdf.text(`Service: ${data.serviceName}`, 20, 135);
    pdf.text(`Date: ${data.serviceDate}`, 20, 142);
    pdf.text(`Heure: ${data.serviceTime}`, 20, 149);
    pdf.text(`Participants: ${data.participants}`, 20, 156);

    // Invoice table header
    const tableStartY = 175;
    pdf.setFillColor(240, 240, 240);
    pdf.rect(20, tableStartY, 170, 10, 'F');
    
    pdf.setFontSize(10);
    pdf.setTextColor(0, 0, 0);
    pdf.text('Description', 25, tableStartY + 7);
    pdf.text('Participants', 100, tableStartY + 7);
    pdf.text('Prix unitaire', 130, tableStartY + 7);
    pdf.text('Total', 165, tableStartY + 7);

    // Invoice table content
    const rowY = tableStartY + 15;
    pdf.text(data.serviceName, 25, rowY);
    pdf.text(data.participants.toString(), 105, rowY);
    pdf.text(`${data.unitPrice}€`, 135, rowY);
    pdf.text(`${data.totalAmount}€`, 165, rowY);

    // Draw table lines
    pdf.setDrawColor(200, 200, 200);
    pdf.line(20, tableStartY, 190, tableStartY); // Top line
    pdf.line(20, tableStartY + 10, 190, tableStartY + 10); // Header bottom
    pdf.line(20, rowY + 5, 190, rowY + 5); // Content bottom
    pdf.line(20, tableStartY, 20, rowY + 5); // Left line
    pdf.line(190, tableStartY, 190, rowY + 5); // Right line
    pdf.line(95, tableStartY, 95, rowY + 5); // Column separator 1
    pdf.line(125, tableStartY, 125, rowY + 5); // Column separator 2
    pdf.line(160, tableStartY, 160, rowY + 5); // Column separator 3

    // Total section
    const totalY = rowY + 20;
    pdf.setFontSize(12);
    pdf.setTextColor(0, 0, 0);
    pdf.text('Total:', 140, totalY);
    pdf.text(`${data.totalAmount} ${data.currency}`, 165, totalY);

    // Payment status
    const paymentY = totalY + 15;
    pdf.setFontSize(11);
    pdf.setTextColor(16, 185, 129);
    
    let statusText = '';
    let statusColor: [number, number, number] = [16, 185, 129];
    
    switch (data.paymentStatus) {
      case 'paid':
        statusText = 'PAYÉ';
        statusColor = [16, 185, 129];
        break;
      case 'partial':
        statusText = 'PARTIELLEMENT PAYÉ';
        statusColor = [245, 158, 11];
        break;
      case 'pending':
        statusText = 'EN ATTENTE DE PAIEMENT';
        statusColor = [239, 68, 68];
        break;
    }
    
    pdf.setTextColor(...statusColor);
    pdf.text(`Statut: ${statusText}`, 20, paymentY);

    if (data.paidAmount && data.paymentStatus !== 'paid') {
      pdf.setTextColor(0, 0, 0);
      pdf.text(`Montant payé: ${data.paidAmount}€`, 20, paymentY + 7);
      if (data.remainingAmount) {
        pdf.text(`Solde restant: ${data.remainingAmount}€`, 20, paymentY + 14);
      }
    }

    if (data.paymentDate && data.paymentMethod) {
      pdf.setTextColor(100, 100, 100);
      pdf.text(`Paiement reçu le ${data.paymentDate} par ${data.paymentMethod}`, 20, paymentY + 21);
    }

    // Notes section
    if (data.notes) {
      const notesY = paymentY + 35;
      pdf.setFontSize(10);
      pdf.setTextColor(100, 100, 100);
      pdf.text('Notes:', 20, notesY);
      
      const noteLines = data.notes.split('\n');
      let currentY = notesY + 7;
      noteLines.forEach(line => {
        pdf.text(line, 20, currentY);
        currentY += 5;
      });
    }

    // Footer
    pdf.setFontSize(8);
    pdf.setTextColor(150, 150, 150);
    pdf.text('Merci pour votre confiance !', 20, 270);
    pdf.text('Soleil & Découverte - Excursions éco-responsables', 20, 275);
    pdf.text('Guadeloupe, France', 20, 280);
    pdf.text(`Facture générée le ${new Date().toLocaleDateString('fr-FR')}`, 130, 280);

    // Convert to blob
    const pdfBlob = pdf.output('blob');
    return pdfBlob;

  } catch (error) {
    console.error('Error generating invoice PDF:', error);
    throw new Error('Failed to generate invoice PDF');
  }
}

/**
 * Generate invoice number based on date and reservation
 */
export function generateInvoiceNumber(reservationNumber: string, date: Date = new Date()): string {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  
  // Extract last 4 characters of reservation number for uniqueness
  const reservationSuffix = reservationNumber.slice(-4);
  
  return `INV-${year}${month}${day}-${reservationSuffix}`;
}

/**
 * Generate invoice data from reservation and payment information
 */
export function createInvoiceData(
  reservation: any,
  payment: any,
  customer: any,
  service: any
): InvoiceData {
  const invoiceDate = new Date().toLocaleDateString('fr-FR');
  const serviceDate = new Date(reservation.start_time).toLocaleDateString('fr-FR');
  const serviceTime = new Date(reservation.start_time).toLocaleTimeString('fr-FR', {
    hour: '2-digit',
    minute: '2-digit'
  });

  let paymentStatus: 'paid' | 'partial' | 'pending' = 'pending';
  let paidAmount = 0;
  let remainingAmount = reservation.total_amount;

  if (payment) {
    paidAmount = payment.amount;
    if (payment.is_deposit) {
      paymentStatus = 'partial';
      remainingAmount = reservation.remaining_amount || (reservation.total_amount - payment.amount);
    } else {
      paymentStatus = 'paid';
      remainingAmount = 0;
    }
  }

  return {
    invoiceNumber: generateInvoiceNumber(reservation.reservation_number),
    invoiceDate,
    customerName: `${customer.first_name} ${customer.last_name}`,
    customerEmail: customer.email,
    reservationNumber: reservation.reservation_number,
    serviceName: service.name,
    serviceDate,
    serviceTime,
    participants: reservation.participant_count,
    unitPrice: reservation.total_amount / reservation.participant_count,
    totalAmount: reservation.total_amount,
    currency: 'EUR',
    paymentStatus,
    paidAmount: paidAmount > 0 ? paidAmount : undefined,
    remainingAmount: remainingAmount > 0 ? remainingAmount : undefined,
    paymentDate: payment?.payment_date ? new Date(payment.payment_date).toLocaleDateString('fr-FR') : undefined,
    paymentMethod: payment ? 'Carte bancaire' : undefined,
  };
}
