import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const reservationId = params.id;

    if (!reservationId) {
      return NextResponse.json(
        { error: 'Reservation ID is required' },
        { status: 400 }
      );
    }

    // Get all payments for this reservation
    const { data: payments, error: paymentsError } = await supabase
      .from('payments')
      .select('*')
      .eq('reservation_id', reservationId)
      .order('created_at', { ascending: false });

    if (paymentsError) {
      console.error('Error fetching payments:', paymentsError);
      return NextResponse.json(
        { error: 'Failed to fetch payments' },
        { status: 500 }
      );
    }

    // Get refunds for this reservation
    const { data: refunds, error: refundsError } = await supabase
      .from('refunds')
      .select('*')
      .eq('reservation_id', reservationId)
      .order('created_at', { ascending: false });

    if (refundsError) {
      console.error('Error fetching refunds:', refundsError);
      return NextResponse.json(
        { error: 'Failed to fetch refunds' },
        { status: 500 }
      );
    }

    // Calculate payment summary
    const totalPaid = payments
      .filter(p => p.status === 'succeeded')
      .reduce((sum, p) => sum + p.amount, 0);

    const totalRefunded = refunds
      .filter(r => r.status === 'completed')
      .reduce((sum, r) => sum + r.refund_amount, 0);

    const pendingPayments = payments.filter(p => 
      ['pending', 'processing', 'requires_action'].includes(p.status)
    );

    const failedPayments = payments.filter(p => 
      ['failed', 'canceled', 'requires_payment_method'].includes(p.status)
    );

    return NextResponse.json({
      success: true,
      payments: payments || [],
      refunds: refunds || [],
      summary: {
        totalPaid,
        totalRefunded,
        netAmount: totalPaid - totalRefunded,
        pendingCount: pendingPayments.length,
        failedCount: failedPayments.length,
        hasSuccessfulPayment: totalPaid > 0,
        hasPendingPayments: pendingPayments.length > 0,
        hasFailedPayments: failedPayments.length > 0,
      },
    });

  } catch (error) {
    console.error('Error getting payment history:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
