import { NextRequest, NextResponse } from 'next/server'

export const dynamic = 'force-dynamic';
import { supabaseAdmin } from '@/lib/supabase'
import { withAdminAuth } from '@/lib/admin-auth'

// GET /api/admin/settings - Get business settings (admin/manager only)
export const GET = withAdminAuth(async (request: NextRequest) => {
  try {
    const { searchParams } = new URL(request.url)
    const category = searchParams.get('category')
    const publicOnly = searchParams.get('public') === 'true'

    if (!supabaseAdmin) {
      return NextResponse.json({ error: 'Database connection not available' }, { status: 500 })
    }

    let query = supabaseAdmin.from('business_settings').select('*')

    // Filter by category if specified
    if (category) {
      query = query.eq('category', category)
    }

    // If public only, filter to public settings
    if (publicOnly) {
      query = query.eq('is_public', true)
    }

    const { data: settings, error } = await query.order('category').order('key')

    if (error) {
      console.error('Error fetching settings:', error)
      return NextResponse.json({ error: 'Failed to fetch settings' }, { status: 500 })
    }

    // Transform settings into a more usable format
    const settingsMap: Record<string, any> = {}
    const categorizedSettings: Record<string, Record<string, any>> = {}

    settings?.forEach((setting: any) => {
      let value = setting.value

      // Parse value based on type
      switch (setting.value_type) {
        case 'number':
          value = parseFloat(setting.value)
          break
        case 'boolean':
          value = setting.value === 'true'
          break
        case 'json':
          try {
            value = JSON.parse(setting.value)
          } catch {
            value = setting.value
          }
          break
        default:
          value = setting.value
      }

      settingsMap[setting.key] = value

      if (!categorizedSettings[setting.category]) {
        categorizedSettings[setting.category] = {}
      }
      categorizedSettings[setting.category][setting.key] = {
        value,
        description: setting.description,
        type: setting.value_type,
        isPublic: setting.is_public
      }
    })

    return NextResponse.json({
      settings: settingsMap,
      categorized: categorizedSettings,
      count: settings?.length || 0
    })

  } catch (error) {
    console.error('Settings GET error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}, 'settings:read')

// PUT /api/admin/settings - Update business settings (admin only)
export const PUT = withAdminAuth(async (request: NextRequest) => {
  try {
    if (!supabaseAdmin) {
      return NextResponse.json({ error: 'Database connection not available' }, { status: 500 })
    }

    const updates = await request.json()

    if (!updates || typeof updates !== 'object') {
      return NextResponse.json({ error: 'Invalid request body' }, { status: 400 })
    }

    const results = []
    const errors = []

    // Process each setting update
    for (const [key, value] of Object.entries(updates)) {
      try {
        // Get current setting to determine type
        const { data: currentSetting } = await supabaseAdmin
          .from('business_settings')
          .select('value_type')
          .eq('key', key)
          .single()

        if (!currentSetting) {
          errors.push({ key, error: 'Setting not found' })
          continue
        }

        // Convert value to string based on type
        let stringValue: string
        switch (currentSetting.value_type) {
          case 'json':
            stringValue = JSON.stringify(value)
            break
          case 'boolean':
            stringValue = Boolean(value).toString()
            break
          case 'number':
            stringValue = Number(value).toString()
            break
          default:
            stringValue = String(value)
        }

        // Update the setting
        const { data, error } = await supabaseAdmin
          .from('business_settings')
          .update({ value: stringValue })
          .eq('key', key)
          .select()
          .single()

        if (error) {
          errors.push({ key, error: error.message })
        } else {
          results.push({ key, updated: true, data })
        }

      } catch (error: any) {
        errors.push({ key, error: error.message })
      }
    }

    return NextResponse.json({
      success: errors.length === 0,
      updated: results.length,
      results,
      errors: errors.length > 0 ? errors : undefined
    })

  } catch (error) {
    console.error('Settings PUT error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}, 'settings:write')

// POST /api/admin/settings - Create new business setting (admin only)
export const POST = withAdminAuth(async (request: NextRequest) => {
  try {
    if (!supabaseAdmin) {
      return NextResponse.json({ error: 'Database connection not available' }, { status: 500 })
    }

    const { key, value, value_type = 'string', category = 'general', description, is_public = false } = await request.json()

    if (!key || value === undefined) {
      return NextResponse.json({ error: 'Key and value are required' }, { status: 400 })
    }

    // Convert value to string based on type
    let stringValue: string
    switch (value_type) {
      case 'json':
        stringValue = JSON.stringify(value)
        break
      case 'boolean':
        stringValue = Boolean(value).toString()
        break
      case 'number':
        stringValue = Number(value).toString()
        break
      default:
        stringValue = String(value)
    }

    const { data, error } = await supabaseAdmin
      .from('business_settings')
      .insert({
        key,
        value: stringValue,
        value_type,
        category,
        description,
        is_public
      })
      .select()
      .single()

    if (error) {
      if (error.code === '23505') { // Unique constraint violation
        return NextResponse.json({ error: 'Setting key already exists' }, { status: 409 })
      }
      console.error('Error creating setting:', error)
      return NextResponse.json({ error: 'Failed to create setting' }, { status: 500 })
    }

    return NextResponse.json({ setting: data }, { status: 201 })

  } catch (error) {
    console.error('Settings POST error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}, 'settings:write')
