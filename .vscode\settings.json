{"typescript.preferences.includePackageJsonAutoImports": "off", "typescript.suggest.autoImports": false, "editor.codeActionsOnSave": {"source.organizeImports": false, "source.removeUnusedImports": false}, "typescript.preferences.organizeImportsIgnoreCase": false, "typescript.preferences.organizeImportsCollation": "unicode", "typescript.preferences.organizeImportsNumericCollation": false, "typescript.updateImportsOnFileMove.enabled": "never", "editor.formatOnSave": true, "editor.formatOnPaste": false, "editor.formatOnType": false, "[typescript]": {"editor.codeActionsOnSave": {"source.organizeImports": false, "source.removeUnusedImports": false}}, "[typescriptreact]": {"editor.codeActionsOnSave": {"source.organizeImports": false, "source.removeUnusedImports": false}}}