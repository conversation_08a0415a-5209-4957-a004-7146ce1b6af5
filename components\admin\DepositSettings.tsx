"use client";

import { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Separator } from '@/components/ui/separator';
import { Loader2, Save, RotateCcw, Percent, Euro, AlertCircle, CheckCircle } from 'lucide-react';
import { DepositSettings as DepositSettingsType } from '@/lib/deposit-settings';

export function DepositSettings() {
  const [settings, setSettings] = useState<DepositSettingsType | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // Form state
  const [depositPercentage, setDepositPercentage] = useState<number>(20);
  const [isDepositEnabled, setIsDepositEnabled] = useState<boolean>(true);
  const [minimumDepositAmount, setMinimumDepositAmount] = useState<string>('');
  const [maximumDepositAmount, setMaximumDepositAmount] = useState<string>('');

  useEffect(() => {
    loadSettings();
  }, []);

  const loadSettings = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch('/api/admin/deposit-settings');
      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Failed to load settings');
      }

      const settingsData = result.settings;
      setSettings(settingsData);
      setDepositPercentage(settingsData.depositPercentage);
      setIsDepositEnabled(settingsData.isDepositEnabled);
      setMinimumDepositAmount(settingsData.minimumDepositAmount?.toString() || '');
      setMaximumDepositAmount(settingsData.maximumDepositAmount?.toString() || '');
    } catch (err) {
      console.error('Error loading deposit settings:', err);
      setError(err instanceof Error ? err.message : 'Failed to load settings');
    } finally {
      setLoading(false);
    }
  };

  const saveSettings = async () => {
    try {
      setSaving(true);
      setError(null);
      setSuccess(null);

      const updateData: any = {
        depositPercentage,
        isDepositEnabled,
      };

      if (minimumDepositAmount) {
        updateData.minimumDepositAmount = parseFloat(minimumDepositAmount);
      }

      if (maximumDepositAmount) {
        updateData.maximumDepositAmount = parseFloat(maximumDepositAmount);
      }

      const response = await fetch('/api/admin/deposit-settings', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updateData),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Failed to save settings');
      }

      setSettings(result.settings);
      setSuccess('Paramètres d\'acompte mis à jour avec succès');
      
      // Clear success message after 3 seconds
      setTimeout(() => setSuccess(null), 3000);
    } catch (err) {
      console.error('Error saving deposit settings:', err);
      setError(err instanceof Error ? err.message : 'Failed to save settings');
    } finally {
      setSaving(false);
    }
  };

  const resetToDefaults = async () => {
    try {
      setSaving(true);
      setError(null);
      setSuccess(null);

      const response = await fetch('/api/admin/deposit-settings', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ action: 'reset' }),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Failed to reset settings');
      }

      const settingsData = result.settings;
      setSettings(settingsData);
      setDepositPercentage(settingsData.depositPercentage);
      setIsDepositEnabled(settingsData.isDepositEnabled);
      setMinimumDepositAmount('');
      setMaximumDepositAmount('');
      setSuccess('Paramètres réinitialisés aux valeurs par défaut');
      
      // Clear success message after 3 seconds
      setTimeout(() => setSuccess(null), 3000);
    } catch (err) {
      console.error('Error resetting deposit settings:', err);
      setError(err instanceof Error ? err.message : 'Failed to reset settings');
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-center">
            <Loader2 className="w-6 h-6 animate-spin text-emerald-500 mr-3" />
            <span className="text-gray-600">Chargement des paramètres...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Percent className="w-5 h-5" />
          Paramètres d'acompte
        </CardTitle>
        <p className="text-sm text-gray-600">
          Configurez les options de paiement par acompte pour vos clients
        </p>
      </CardHeader>
      <CardContent className="space-y-6">
        {error && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {success && (
          <Alert className="border-emerald-200 bg-emerald-50">
            <CheckCircle className="h-4 w-4 text-emerald-600" />
            <AlertDescription className="text-emerald-800">{success}</AlertDescription>
          </Alert>
        )}

        {/* Enable/Disable Deposits */}
        <div className="flex items-center justify-between">
          <div className="space-y-1">
            <Label htmlFor="deposit-enabled" className="text-base font-medium">
              Activer les acomptes
            </Label>
            <p className="text-sm text-gray-600">
              Permettre aux clients de payer par acompte
            </p>
          </div>
          <Switch
            id="deposit-enabled"
            checked={isDepositEnabled}
            onCheckedChange={setIsDepositEnabled}
          />
        </div>

        <Separator />

        {/* Deposit Percentage */}
        <div className="space-y-2">
          <Label htmlFor="deposit-percentage" className="text-base font-medium">
            Pourcentage d'acompte par défaut
          </Label>
          <div className="flex items-center space-x-2">
            <div className="relative flex-1 max-w-xs">
              <Input
                id="deposit-percentage"
                type="number"
                min="0"
                max="100"
                value={depositPercentage}
                onChange={(e) => setDepositPercentage(Number(e.target.value))}
                className="pr-8"
                disabled={!isDepositEnabled}
              />
              <Percent className="absolute right-2 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
            </div>
          </div>
          <p className="text-sm text-gray-600">
            Pourcentage du montant total à payer en acompte (0-100%)
          </p>
        </div>

        <Separator />

        {/* Minimum and Maximum Amounts */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="minimum-amount" className="text-base font-medium">
              Montant minimum d'acompte
            </Label>
            <div className="relative">
              <Input
                id="minimum-amount"
                type="number"
                min="0"
                step="0.01"
                value={minimumDepositAmount}
                onChange={(e) => setMinimumDepositAmount(e.target.value)}
                placeholder="Optionnel"
                className="pl-8"
                disabled={!isDepositEnabled}
              />
              <Euro className="absolute left-2 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
            </div>
            <p className="text-sm text-gray-600">
              Montant minimum requis pour un acompte
            </p>
          </div>

          <div className="space-y-2">
            <Label htmlFor="maximum-amount" className="text-base font-medium">
              Montant maximum d'acompte
            </Label>
            <div className="relative">
              <Input
                id="maximum-amount"
                type="number"
                min="0"
                step="0.01"
                value={maximumDepositAmount}
                onChange={(e) => setMaximumDepositAmount(e.target.value)}
                placeholder="Optionnel"
                className="pl-8"
                disabled={!isDepositEnabled}
              />
              <Euro className="absolute left-2 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
            </div>
            <p className="text-sm text-gray-600">
              Montant maximum autorisé pour un acompte
            </p>
          </div>
        </div>

        <Separator />

        {/* Preview */}
        {isDepositEnabled && (
          <div className="bg-blue-50 p-4 rounded-lg">
            <h4 className="font-medium text-blue-900 mb-2">Aperçu</h4>
            <p className="text-sm text-blue-800">
              Pour une réservation de 100€, l'acompte sera de {depositPercentage}€ 
              ({depositPercentage}%), et le solde restant de {100 - depositPercentage}€.
            </p>
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex justify-between pt-4">
          <Button
            variant="outline"
            onClick={resetToDefaults}
            disabled={saving}
            className="flex items-center gap-2"
          >
            <RotateCcw className="w-4 h-4" />
            Réinitialiser
          </Button>

          <Button
            onClick={saveSettings}
            disabled={saving}
            className="flex items-center gap-2 bg-emerald-500 hover:bg-emerald-600"
          >
            {saving ? (
              <Loader2 className="w-4 h-4 animate-spin" />
            ) : (
              <Save className="w-4 h-4" />
            )}
            {saving ? 'Enregistrement...' : 'Enregistrer'}
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
