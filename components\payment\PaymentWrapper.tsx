"use client";

import { Alert, AlertDescription } from "@/components/ui/alert";
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { PaymentType } from "@/lib/types/payments";
import { AlertCircle, Loader2 } from "lucide-react";
import { useEffect, useState } from "react";
import { PaymentForm } from "./PaymentForm";
import { StripeProvider } from "./StripeProvider";

interface PaymentWrapperProps {
	reservationId: string;
	amount: number; // in euros
	currency?: string;
	paymentType?: PaymentType;
	onSuccess: (paymentIntentId: string) => void;
	onError: (error: string) => void;
}

export function PaymentWrapper({
	reservationId,
	amount,
	currency = "EUR",
	paymentType = "full",
	onSuccess,
	onError,
}: PaymentWrapperProps) {
	const [clientSecret, setClientSecret] = useState<string | null>(null);
	const [isLoading, setIsLoading] = useState(true);
	const [error, setError] = useState<string | null>(null);
	const [retryCount, setRetryCount] = useState(0);

	useEffect(() => {
		const createPaymentIntent = async () => {
			try {
				setIsLoading(true);
				setError(null);

				console.log("=== PAYMENT WRAPPER DEBUG ===");
				console.log("Creating payment intent with:", { reservationId, paymentType, amount });

				const response = await fetch("/api/payments/create-intent", {
					method: "POST",
					headers: {
						"Content-Type": "application/json",
					},
					body: JSON.stringify({
						reservationId,
						paymentType,
						amount,
					}),
				});

				const result = await response.json();
				console.log("Payment intent response:", result);

				if (!response.ok) {
					console.log("Payment intent request failed:", response.status, result);
					throw new Error(result.error || "Failed to create payment intent");
				}

				if (result.success && result.clientSecret) {
					console.log("Setting client secret:", result.clientSecret.substring(0, 20) + "...");
					setClientSecret(result.clientSecret);
				} else {
					console.log("Invalid payment intent response:", result);
					throw new Error("Invalid response from payment service");
				}
			} catch (err) {
				console.error("Error creating payment intent:", err);
				const errorMessage = err instanceof Error ? err.message : "Failed to initialize payment";
				setError(errorMessage);
				onError(errorMessage);
			} finally {
				setIsLoading(false);
			}
		};

		if (reservationId) {
			createPaymentIntent();
		}
	}, [reservationId, onError, retryCount]);

	const handleRetry = async () => {
		try {
			setIsLoading(true);
			setError(null);

			const response = await fetch("/api/payments/retry", {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
				},
				body: JSON.stringify({
					reservationId,
				}),
			});

			const result = await response.json();

			if (!response.ok) {
				throw new Error(result.error || "Failed to retry payment");
			}

			if (result.success && result.clientSecret) {
				setClientSecret(result.clientSecret);
				setRetryCount(result.retryCount || 0);
			} else {
				throw new Error("Invalid response from retry service");
			}
		} catch (err) {
			console.error("Error retrying payment:", err);
			const errorMessage = err instanceof Error ? err.message : "Failed to retry payment";
			setError(errorMessage);
			onError(errorMessage);
		} finally {
			setIsLoading(false);
		}
	};

	if (isLoading) {
		return (
			<Card>
				<CardContent className="p-6">
					<div className="flex items-center justify-center">
						<Loader2 className="w-6 h-6 animate-spin text-emerald-500 mr-3" />
						<span className="text-gray-600">Initialisation du paiement...</span>
					</div>
				</CardContent>
			</Card>
		);
	}

	if (error) {
		return (
			<Card>
				<CardHeader>
					<CardTitle className="text-red-600">Erreur de paiement</CardTitle>
				</CardHeader>
				<CardContent>
					<Alert variant="destructive">
						<AlertCircle className="h-4 w-4" />
						<AlertDescription>{error}</AlertDescription>
					</Alert>
				</CardContent>
			</Card>
		);
	}

	if (!clientSecret) {
		return (
			<Card>
				<CardContent className="p-6">
					<Alert variant="destructive">
						<AlertCircle className="h-4 w-4" />
						<AlertDescription>Impossible d'initialiser le paiement. Veuillez réessayer.</AlertDescription>
					</Alert>
				</CardContent>
			</Card>
		);
	}

	return (
		<StripeProvider clientSecret={clientSecret}>
			<PaymentForm
				amount={amount}
				currency={currency}
				reservationId={reservationId}
				onSuccess={onSuccess}
				onError={onError}
				onRetry={handleRetry}
			/>
		</StripeProvider>
	);
}
