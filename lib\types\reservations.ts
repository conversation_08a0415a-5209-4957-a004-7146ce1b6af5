export type Reservations = {
	Row: {
		id: string;
		customer_id: string;
		reservation_number: string;
		participant_count: number;
		total_amount: number;
		currency: string;
		status: string;
		special_requests: string | null;
		discount_code: string | null;
		discount_amount: number;
		check_in_time: string | null;
		qr_code: string | null;
		created_at: string | null;
		updated_at: string | null;
		service_id: string;
		assigned_employee_id: string | null;
		start_time: string;
		end_time: string;
		booking_source: string | null;
		admin_notes: string | null;
		requires_confirmation: boolean | null;
		confirmed_at: string | null;
		confirmed_by: string | null;
		// Deposit-related fields
		deposit_amount: number | null;
		remaining_amount: number | null;
		deposit_paid: boolean | null;
		// Service options
		selected_options: any; // Array of selected option IDs
		deposit_payment_id: string | null;
	};
	Insert: Partial<Reservations["Row"]> & {
		customer_id: string;
		reservation_number: string;
		total_amount: number;
		service_id: string;
		start_time: string;
		end_time: string;
	};
	Update: Partial<Reservations["Row"]>;
};

export type ReservationStatusHistory = {
	Row: {
		id: string;
		reservation_id: string;
		old_status: string | null;
		new_status: string;
		changed_by: string | null;
		change_reason: string | null;
		automated_change: boolean | null;
		metadata: import("./common").Json | null;
		created_at: string | null;
	};
	Insert: Partial<ReservationStatusHistory["Row"]> & {
		reservation_id: string;
		new_status: string;
	};
	Update: Partial<ReservationStatusHistory["Row"]>;
};

export type Payments = {
	Row: {
		id: string;
		reservation_id: string;
		payment_method: string;
		payment_intent_id: string | null;
		amount: number;
		currency: string;
		status: string;
		payment_date: string | null;
		failure_reason: string | null;
		created_at: string | null;
		updated_at: string | null;
		// Deposit-related fields
		payment_type: "full" | "deposit" | "remaining" | null;
		deposit_percentage: number | null;
		is_deposit: boolean | null;
	};
	Insert: Partial<Payments["Row"]> & {
		reservation_id: string;
		payment_method: string;
		amount: number;
	};
	Update: Partial<Payments["Row"]>;
};

export type Refunds = {
	Row: {
		id: string;
		payment_id: string;
		reservation_id: string;
		refund_amount: number;
		refund_reason: string;
		refund_method: string;
		status: string;
		processed_by: string | null;
		processed_at: string | null;
		external_refund_id: string | null;
		created_at: string | null;
		updated_at: string | null;
	};
	Insert: Partial<Refunds["Row"]> & {
		payment_id: string;
		reservation_id: string;
		refund_amount: number;
		refund_reason: string;
		refund_method: string;
	};
	Update: Partial<Refunds["Row"]>;
};
